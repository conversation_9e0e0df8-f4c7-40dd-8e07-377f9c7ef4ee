"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { Button } from "@/components/UI/Button";
import {
  HeadingLarge,
  HeadingMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import ChatModal from "../ChatModal";
import Pill from "@/components/globals/DSComponentsV0/Pill";
import RenderChatBubble from "./RenderChatBubble";
import { htmlParser } from "@/utils/htmlParser";

type SlideData = {
  title: string;
  subtitle: string;
  chats: ChatMessage[];
};

type ChatMessage = {
  id: string;
  type: "bot" | "human";
  content: string;
};

type AskAnythingSectionProps = {
  pill: string;
  heading: string;
  subheading: string;
  slides: SlideData[];
};

const AskAnythingSection: React.FC<AskAnythingSectionProps> = ({
  pill,
  heading,
  subheading,
  slides,
}) => {
  const [openModal, setOpenModal] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const chatRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const sectionRef = useRef<HTMLDivElement>(null);

  const registerChatRef = useCallback(
    (key: string) => (element: HTMLDivElement | null) => {
      chatRefs.current[key] = element;
    },
    []
  );

  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => setOpenModal(false);

  // ✅ Smooth auto-scroll down, instant reset to top
  const autoScrollLoop = (refKey: string) => {
    const scrollContainer = chatRefs.current[refKey];
    if (!scrollContainer) return;

    const scrollStep = 1; // scroll speed
    const interval = 20; // smaller = faster

    const scrollInterval = setInterval(() => {
      scrollContainer.scrollTop += scrollStep;

      if (
        scrollContainer.scrollTop + scrollContainer.clientHeight >=
        scrollContainer.scrollHeight
      ) {
        // Instantly jump to top
        scrollContainer.scrollTop = 0;
      }
    }, interval);

    return () => clearInterval(scrollInterval);
  };

  useEffect(() => {
    const cleanupDesktop = autoScrollLoop("desktop");
    const cleanupMobile = autoScrollLoop("mobile");
    return () => {
      cleanupDesktop?.();
      cleanupMobile?.();
    };
  }, []);

  // Intersection Observer for mobile frame animation
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      {
        threshold: 0.3, // Trigger when 30% of the section is visible
        rootMargin: "-10% 0px -10% 0px", // Add some margin for better timing
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  if (!slides.length) return null;
  const activeSlide = slides[0];

  return (
    <div ref={sectionRef}>
      <SectionContainerLarge className="!mb-9 md:!mb-24">
        <div className="flex flex-col">
        <Pill
          pill={pill}
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-secondary-100"
          className="mb-2 md:mb-3"
        />
        <HeadingXLarge
          className="text-primary-800 text-center mb-2 md:mb-5"
          weight="medium"
        >
          {heading}
        </HeadingXLarge>

        {htmlParser(subheading, {
          components: {
            p: HeadingSmall,
          },
          classNames: {
            p: "text-neutral-800 md:text-primary-800 text-center !mb-4 md:!mb-14 font-normal",
          },
        })}
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:flex flex-col items-center w-full">
        <div className="bg-primary-200 overflow-hidden flex flex-col lg:flex-row rounded-md md:rounded-[36px] mx-6 gap-8 px-5 md:px-0 w-full">
          {/* Left */}
          <div className="relative flex flex-col items-center text-center lg:flex-1 ">
            <HeadingLarge
              weight="semibold"
              className="mt-20 text-primary-800 mb-3"
            >
              {activeSlide.title}
            </HeadingLarge>
            <HeadingLarge weight="semibold" className="text-primary-800 mb-6">
              {activeSlide.subtitle}
            </HeadingLarge>

            <div className="relative">
              <div className="flex gap-8 absolute right-0 items-center translate-y-[-20%]">
                <Image
                  src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5abf-bd03-7a35-b071-6344f9450661/Untitled_Artwork 40 2.svg"
                  alt="Badge"
                  width={113}
                  height={122}
                  className=""
                />
                <div
                  onClick={handleOpenModal}
                  className="relative inline-block p-0.5 group z-50 cursor-pointer"
                >
                  <div
                    style={{ filter: "url(#squiggly)" }}
                    className="absolute inset-0 border-[3px] border-primary-800 z-10 rounded-2xl pointer-events-none transition-all duration-300 group-hover:scale-105"
                  />
                  <Button
                    variant="secondary"
                    className="relative px-4 sm:px-6 py-4 sm:py-6 rounded-xl bg-white text-primary-800 transition-all duration-300 group-hover:scale-105 group-hover:bg-neutral-100"
                  >
                    Ask me anything
                  </Button>
                </div>
                <Image
                  src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5ac0-0273-7534-87c1-59b1dc0fd401/Untitled_Artwork 41 1.svg"
                  alt="Badge"
                  width={113}
                  height={122}
                  className="rotate-6"
                />
              </div>
              <Image
                src={
                  "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a7461-f803-7104-a518-175a6488ae69/Gemini_Generated_Image_17mzz117mzz117mz 1 (1).svg"
                }
                alt="Customer support representative"
                width={470}
                height={470}
                className="hidden lg:block rounded-lg"
              />
            </div>
          </div>

          {/* Right */}
          <div className="overflow-clip lg:flex-1 flex justify-end">
            <div className="relative w-full flex justify-start items-center">
              <div className="absolute top-16 right-12">
                <Image
                  src={
                    "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5dd5-497d-7ab4-ba25-9583ccbf78b0/Group 1000001647.svg"
                  }
                  alt="Mobile frame"
                  className="rounded-lg"
                  height={0}
                  width={509}
                />
                <div className="absolute top-16 left-6 right-6 h-[75%] pt-4">
                  <div>
                    <HeadingSmall className="text-primary-800 text-center pb-4">
                      OneAssure AI ChatBot
                    </HeadingSmall>
                  </div>
                  <div
                    ref={registerChatRef("desktop")}
                    className="relative h-full overflow-y-scroll no-scrollbar rounded-t-4xl bg-white p-3 flex flex-col gap-3"
                  >
                    {activeSlide.chats.map((chat) => (
                      <RenderChatBubble key={chat.id} chat={chat} />
                    ))}
                  </div>
                </div>
                <div className="absolute bottom-6 left-6 right-6 h-10 bg-gradient-to-b from-transparent to-primary-200 rounded-b-4xl z-20" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden bg-primary-100 rounded-4xl relative">
        <div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 
             w-[300px] h-[300px] rounded-full
             bg-[radial-gradient(circle,theme(colors.primary-400)_0%,theme(colors.primary-200)_50%,transparent_100%)]
             opacity-60 blur-2xl"
        ></div>
        <div className="relative overflow-hidden flex flex-col items-center py-7 bg-transparent">
          {/* Decorative icons */}
          <Image
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5ac0-0273-7534-87c1-59b1dc0fd401/Untitled_Artwork 41 1.svg"
            alt="Claims icon"
            width={60}
            height={60}
            className="absolute top-52 right-6 rotate-6"
          />
          <Image
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5abf-bd03-7a35-b071-6344f9450661/Untitled_Artwork 40 2.svg"
            alt="Shield"
            width={60}
            height={60}
            className="absolute top-24 left-6 rotate-6"
          />

          {/* Heading */}
          <div className="mb-6">
            <HeadingMedium className="text-primary-800 font-medium text-center">
              {activeSlide.title}
            </HeadingMedium>
            <HeadingMedium className="text-primary-800 font-medium text-center">
              {activeSlide.subtitle}
            </HeadingMedium>
          </div>

          {/* Chat section */}
          <div className="flex flex-col gap-6 w-full px-2 mb-9">
            {activeSlide.chats.slice(0, 2).map((chat) => (
              <RenderChatBubble key={chat.id} chat={chat} />
            ))}
          </div>

          {/* Ask Question button */}
          <div className="relative inline-block p-0.5 cursor-pointer">
            <div
              onClick={handleOpenModal}
              style={{
                filter: "url(#squiggly)",
              }}
              className="absolute inset-0 border-2 border-primary-800 rounded-xl z-10"
            />
            <Button
              variant="secondary"
              className="w-full px-6 py-3 text-sm sm:text-base font-normal rounded-xl bg-white text-primary-800"
            >
              Ask a Question
            </Button>
          </div>
        </div>
      </div>

      {openModal && <ChatModal onClose={handleCloseModal} />}
      </SectionContainerLarge>
    </div>
  );
};

export default AskAnythingSection;
